// Login form validation and modal handling
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('login-form');
    const modal = document.getElementById('custom-modal');
    const modalOkButton = document.getElementById('modal-ok-button');
    const loginError = document.getElementById('login-error');

    // Check URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    
    // Handle error messages
    if (urlParams.has('error')) {
        const errorType = urlParams.get('error');
        let errorMessage = 'An error occurred. Please try again.';
        
        switch(errorType) {
            case 'invalid_username':
                errorMessage = 'Invalid username. Please try again.';
                break;
            case 'invalid_credentials':
                errorMessage = 'Invalid username or password. Please try again.';
                break;
            case 'db_error':
                errorMessage = 'Database error. Please try again later.';
                break;
            case 'pending_approval':
                errorMessage = 'Your account is pending admin approval. Please wait for approval before logging in.';
                break;
            case 'account_rejected':
                errorMessage = 'Your account has been rejected by an administrator. Please contact support for more information.';
                break;
            case 'banned':
                errorMessage = 'Your account has been banned. Please contact support for more information.';
                break;
        }
        
        showModal('Login Failed', errorMessage);
    }
    
    // Handle success messages
    if (urlParams.has('success')) {
        const successType = urlParams.get('success');
        let successMessage = 'Operation completed successfully.';
        
        switch(successType) {
            case 'account_created':
                successMessage = 'Account created successfully! You can now login.';
                break;
            case 'account_pending_approval':
                successMessage = 'Account created successfully! Your account is pending admin approval. You will be able to login once approved.';
                break;
        }
        
        showModal('Success', successMessage);
    }

    // Handle form submission
    if (loginForm) {
        loginForm.addEventListener('submit', function(event) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            // Basic validation
            if (!username || !password) {
                event.preventDefault();
                loginError.textContent = 'Please enter both username and password';
                loginError.style.display = 'block';
                return false;
            }
            
            // Clear any previous error messages
            loginError.textContent = '';
            loginError.style.display = 'none';
            
            // Form will submit normally to the PHP handler
        });
    }

    // Modal handling
    if (modalOkButton) {
        modalOkButton.addEventListener('click', function() {
            closeModal();
        });
    }

    // Close modal when clicking outside of it
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            closeModal();
        }
    });

    // Close modal when pressing Escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && modal && modal.style.display === 'block') {
            closeModal();
        }
    });

    // Show modal with custom message
    function showModal(title, message) {
        const modalTitle = document.getElementById('modal-title');
        const modalBody = document.getElementById('modal-body');
        
        if (modalTitle) modalTitle.textContent = title;
        if (modalBody) modalBody.textContent = message;
        
        if (modal) modal.style.display = 'block';
    }

    // Close the modal
    function closeModal() {
        if (modal) modal.style.display = 'none';
        
        // Clean up URL parameters after closing modal
        const url = new URL(window.location.href);
        url.search = '';
        window.history.replaceState({}, document.title, url.toString());
    }

    // Expose functions for potential use by PHP
    window.showLoginModal = showModal;
    window.closeLoginModal = closeModal;
}); 