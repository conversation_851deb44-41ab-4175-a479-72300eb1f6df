// Global variables
let users = [];
let filteredUsers = [];
let currentPage = 1;
let usersPerPage = 50;
let expLevels = []; // Store experience levels from database
let maxExpLevel = 10; // Default value, will be updated from database

// Add global state for ban modal
let banUserId = null;

// API Configuration

// API Configuration
const API_BASE_URL = '../../php/admin/';

// Initialize data from database
async function initializeData() {
    try {
        // Load experience levels first to determine max level
        await loadExpLevels();
        await loadUsers();
        displayUsers();
        // Add event listeners for experience stats hover effects
        addExpHoverEffects();
    } catch (error) {
        console.error('Error initializing data:', error);
        showNotification('Error loading data from database', 'error');
    }
}

// Load experience levels from database
async function loadExpLevels() {
    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=get_exp_levels`);
        const result = await response.json();

        if (result.success && result.data.length > 0) {
            expLevels = result.data;
            
            // Find the highest experience level
            const highestLevel = expLevels.reduce((max, level) => {
                const levelNum = parseInt(level.expName);
                return levelNum > max ? levelNum : max;
            }, 0);
            
            maxExpLevel = highestLevel;
            console.log(`Maximum experience level set to: ${maxExpLevel}`);
        } else {
            console.warn('No experience levels found, using default max level');
        }
    } catch (error) {
        console.error('Error loading experience levels:', error);
        // Keep default maxExpLevel if there's an error
    }
}

function mapBackendUserToFrontend(backendUser) {
    const expLevel = parseInt(backendUser.expLevel) || 1;
    const exp = parseInt(backendUser.userExp) || 0;
    
    return {
        id: backendUser.user_id,
        username: backendUser.username,
        email: backendUser.email || 'N/A',
        name: backendUser.name || 'N/A',
        bio: backendUser.bio || 'No bio available',
        avatar: backendUser.avatar || null,
        password: '********', // Never show actual passwords
        achievements: parseInt(backendUser.achievements) || 0,
        totalAchievements: parseInt(backendUser.totalAchievements) || 50,
        levelsCompleted: parseInt(backendUser.levels_completed) || 0,
        totalLevels: parseInt(backendUser.total_levels) || 6,
        exp: exp,
        expLevel: expLevel,
        isBanned: backendUser.is_banned == 1,
        bannedFrom: backendUser.banned_from,
        bannedUntil: backendUser.banned_until,
        isApproved: parseInt(backendUser.is_approved) || 0,
        ...calculateExpThresholds(exp, expLevel),
        joinDate: new Date(backendUser.created_at)
    };
}

function getApprovalStatus(isApproved) {
    switch (isApproved) {
        case 1:
            return {
                status: 'approved',
                text: 'Approved',
                badge: `<div class="approved-badge"><i class="fas fa-check"></i> Approved</div>`
            };
        case -1:
            return {
                status: 'rejected',
                text: 'Rejected',
                badge: `<div class="pending-badge" style="background-color: #dc3545;"><i class="fas fa-times"></i> Rejected</div>`
            };
        case 0:
        default:
            return {
                status: 'pending',
                text: 'Pending Approval',
                badge: `<div class="pending-badge"><i class="fas fa-clock"></i> Pending</div>`
            };
    }
}

// API Functions
async function loadUsers() {
    try {
        const response = await fetch(`${API_BASE_URL}users_api.php?action=get_all_users`);
        const result = await response.json();

        if (result.success) {
            users = result.data.map(mapBackendUserToFrontend);
            filteredUsers = [...users];
        } else {
            throw new Error(result.error || 'Failed to load users');
        }
    } catch (error) {
        console.error('Error loading users:', error);
        // Fallback to empty array
        users = [];
        filteredUsers = [];
    }
}

// Users management functions
function displayUsers() {
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const usersToShow = filteredUsers.slice(startIndex, endIndex);

    const usersGrid = document.getElementById('usersGrid');
    usersGrid.innerHTML = '';

    if (usersToShow.length === 0) {
        usersGrid.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>No Users Found</h3>
                <p>No users match your search criteria or no users exist yet.</p>
            </div>
        `;
        updatePagination();
        return;
    }

    usersToShow.forEach(user => {
        const userCard = createUserCard(user);
        usersGrid.appendChild(userCard);
    });

    updatePagination();
    
    // Re-add hover effects after displaying users
    addExpHoverEffects();
}

function createUserCard(user) {
    // Check if ban has expired
    let isActuallyBanned = user.isBanned;
    if (user.isBanned && user.bannedUntil) {
        const now = new Date();
        const bannedUntilDate = new Date(user.bannedUntil);
        if (bannedUntilDate < now) {
            isActuallyBanned = false;
        }
    }

    // Determine approval status
    const approvalStatus = getApprovalStatus(user.isApproved);

    const card = document.createElement('div');
    card.className = 'user-card';
    if (isActuallyBanned) {
        card.classList.add('banned-card');
    }
    card.onclick = () => showUserDetails(user);

    // Calculate experience percentage using the pre-calculated values
    const expPercentage = ((user.exp - user.currentLevelExp) / (user.nextLevelExp - user.currentLevelExp)) * 100;

    card.innerHTML = `
        ${isActuallyBanned ? `
            <div class="ban-badge">
                <i class="fas fa-ban"></i> Banned
            </div>
        ` : approvalStatus.badge}
        <div class="user-card-header">
            <div class="user-info">
                <h3>${user.username}</h3>
                <p>${user.email}</p>
            </div>
            <div class="user-actions">
                <button class="three-dots" onclick="toggleDropdown(event, ${user.id})">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
                <div class="dropdown-menu" id="dropdown-${user.id}">
                    <a href="#" class="dropdown-item" onclick="event.stopPropagation(); showUserDetails(${JSON.stringify(user).replace(/"/g, '&quot;')})">
                        <i class="fas fa-eye"></i> View
                    </a>
                </div>
            </div>
        </div>
        <div class="user-stats">
            <div class="stat">
                <div class="stat-value">${user.achievements}</div>
                <div class="stat-label">Achievements</div>
            </div>
            <div class="stat">
                <div class="stat-value">${user.levelsCompleted}</div>
                <div class="stat-label">Levels</div>
            </div>
            <div class="stat exp-stat">
                <div class="exp-tooltip">
                    ${user.expLevel >= maxExpLevel ? 
                      `EXP: ${user.exp} / Maximum Level Reached!` : 
                      `EXP: ${user.exp} / Next Level: ${user.nextLevelExp}`
                    }
                </div>
                <div class="exp-level-indicator">
                    <span class="exp-level-text">${user.expLevel}</span>
                </div>
                <div class="exp-bar-container">
                    <div class="exp-bar-fill" style="width: ${user.expLevel >= maxExpLevel ? 100 : expPercentage}%"></div>
                </div>
                <div class="stat-label">Experience</div>
            </div>
        </div>
    `;

    return card;
}

function toggleDropdown(event, userId) {
    event.stopPropagation();

    // Close all other dropdowns
    document.querySelectorAll('.dropdown-menu').forEach(menu => {
        if (menu.id !== `dropdown-${userId}`) {
            menu.classList.remove('show');
        }
    });

    // Toggle current dropdown
    const dropdown = document.getElementById(`dropdown-${userId}`);
    dropdown.classList.toggle('show');
}

function showUserDetails(user) {
    // Check if ban has expired
    let isActuallyBanned = user.isBanned;
    if (user.isBanned && user.bannedUntil) {
        const now = new Date();
        const bannedUntilDate = new Date(user.bannedUntil);
        if (bannedUntilDate < now) {
            isActuallyBanned = false;
        }
    }
    const modal = document.getElementById('userModal');
    const modalBody = document.getElementById('userModalBody');
    const modalContent = modal.querySelector('.modal-content');

    // Remove existing ban banner if present
    const existingBanner = modalContent.querySelector('.ban-banner');
    if (existingBanner) {
        modalContent.removeChild(existingBanner);
    }

    // Add ban banner if user is banned
    if (isActuallyBanned) {
        const banBanner = document.createElement('div');
        banBanner.className = 'ban-banner';
        
        let banEndDate;
        if (user.bannedUntil) {
            const now = new Date();
            const bannedUntilDate = new Date(user.bannedUntil);
            const diffMinutes = Math.round((bannedUntilDate - now) / (1000 * 60));

            if (diffMinutes > 0) {
                banEndDate = `in ${diffMinutes} minute(s)`;
            } else {
                banEndDate = "Expired";
            }
        } else {
            banEndDate = 'Permanent';
        }
        
        banBanner.innerHTML = `
            <h3><i class="fas fa-gavel"></i> User Banned</h3>
            <p>This user's access is restricted. Ban ends: <strong>${banEndDate}</strong></p>
        `;
        // Insert banner after the modal header
        modalContent.insertBefore(banBanner, modalBody.parentNode.querySelector('.modal-actions'));
    }

    const achievementProgress = (user.achievements / user.totalAchievements) * 100;
    const levelProgress = (user.levelsCompleted / user.totalLevels) * 100;

    modalBody.innerHTML = `
        <div class="user-detail-container">
            <div class="user-detail-section">
                <h3>Basic Information</h3>
                <div class="user-basic-info">
                    ${user.avatar ? `
                    <div class="info-item">
                        <div class="info-label">Avatar</div>
                        <div class="info-value">
                            <img src="${user.avatar}" alt="User Avatar" style="width: 50px; height: 50px; border-radius: 50%; object-fit: cover;">
                        </div>
                    </div>` : ''}
                    <div class="info-item">
                        <div class="info-label">User ID</div>
                        <div class="info-value">${user.id}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Username</div>
                        <div class="info-value">${user.username}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Email</div>
                        <div class="info-value">${user.email}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Bio</div>
                        <div class="info-value">${user.bio}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Join Date</div>
                        <div class="info-value">${user.joinDate.toLocaleDateString()}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Account Status</div>
                        <div class="info-value">
                            ${(() => {
                                const approvalStatus = getApprovalStatus(user.isApproved);
                                let statusClass = '';
                                switch (approvalStatus.status) {
                                    case 'approved': statusClass = 'approved'; break;
                                    case 'rejected': statusClass = 'rejected'; break;
                                    case 'pending': statusClass = 'pending'; break;
                                }
                                return `<span class="approval-status-badge ${statusClass}">${approvalStatus.text}</span>`;
                            })()}
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Ban Status</div>
                        <div class="info-value">
                            ${isActuallyBanned ? `
                                <span class="ban-status-badge banned">Banned</span>
                            ` : `
                                <span class="ban-status-badge">Not Banned</span>
                            `}
                        </div>
                    </div>
                    ${isActuallyBanned && user.bannedUntil ? `
                    <div class="info-item">
                        <div class="info-label">Ban Ends</div>
                        <div class="info-value">${new Date(user.bannedUntil).toLocaleString()}</div>
                    </div>
                    ` : ''}
                </div>
            </div>

            <div class="user-detail-section">
                <h3>Progress & Achievements</h3>
                <div class="progress-section">
                    <div class="progress-item">
                        <div class="progress-header">
                            <span class="progress-label">Achievements Unlocked</span>
                            <span class="progress-value">${user.achievements}/${user.totalAchievements}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${achievementProgress}%"></div>
                        </div>
                    </div>

                    <div class="progress-item">
                        <div class="progress-header">
                            <span class="progress-label">Levels Completed</span>
                            <span class="progress-value">${user.levelsCompleted}/${user.totalLevels}</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${levelProgress}%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="user-detail-section">
                <h3>User Experience</h3>
                <div class="exp-display">
                    <div class="exp-header">
                        <div class="exp-level">
                            <div class="exp-level-badge">${user.expLevel}</div>
                            <div class="exp-title">
                                Experience ${user.expLevel}
                                ${user.isMaxLevel ? ' <span class="max-level-tag">(MAX)</span>' : ''}
                            </div>
                        </div>
                        <div class="exp-value">${user.exp} EXP</div>
                    </div>
                    
                    <!-- Calculate experience percentage -->
                    ${(() => {
                        const expPercentage = ((user.exp - user.currentLevelExp) / (user.nextLevelExp - user.currentLevelExp)) * 100;
                        const expToNextLevel = user.nextLevelExp - user.exp;
                        
                        // Check if user has reached maximum level
                        const isMaxLevel = user.expLevel >= maxExpLevel;
                        
                        return `
                            <div class="exp-progress-container">
                                <div class="exp-progress-bar" style="width: ${isMaxLevel ? 100 : expPercentage}%"></div>
                            </div>
                            <div class="exp-progress-info">
                                <span>Current: ${user.exp} EXP</span>
                                ${isMaxLevel ?
                                    `<span class="exp-max-level"><i class="fas fa-trophy"></i> Maximum Level Reached!</span>` :
                                    `<span class="exp-next-level">${expToNextLevel} EXP needed to reach experience ${user.expLevel + 1}</span>`
                                }
                            </div>
                        `;
                    })()}
                </div>
            </div>
        </div>
    `;

    // Button and action handling
    const banBtn = document.getElementById('banUserBtn');
    const deleteBtn = document.getElementById('deleteUserBtn');

    if (isActuallyBanned) {
        banBtn.innerHTML = '<i class="fas fa-unlock"></i> Unban User';
        banBtn.classList.remove('ban-btn');
        banBtn.classList.add('unban-btn');
        banBtn.onclick = () => unbanUser(user.id);
    } else {
        banBtn.innerHTML = '<i class="fas fa-ban"></i> Ban User';
        banBtn.classList.remove('unban-btn');
        banBtn.classList.add('ban-btn');
        banBtn.onclick = () => banUser(user.id);
    }

    deleteBtn.onclick = () => deleteUser(user.id);

    // Approval button handling
    const approveBtn = document.getElementById('approveUserBtn');
    const rejectBtn = document.getElementById('rejectUserBtn');

    if (user.isApproved === 0) { // Pending approval
        approveBtn.style.display = 'inline-flex';
        rejectBtn.style.display = 'inline-flex';
        approveBtn.onclick = () => approveUser(user.id);
        rejectBtn.onclick = () => rejectUser(user.id);
    } else {
        approveBtn.style.display = 'none';
        rejectBtn.style.display = 'none';
    }

    modal.classList.add('show');
}

async function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user?')) {
        try {
            const response = await fetch(`${API_BASE_URL}users_api.php?action=delete_user&user_id=${userId}`, {
                method: 'DELETE'
            });
            const result = await response.json();

            if (result.success) {
                users = users.filter(user => user.id !== userId);
                filteredUsers = filteredUsers.filter(user => user.id !== userId);
                displayUsers();
                closeModal('userModal'); // Close the user details modal
                showNotification('User deleted successfully', 'success');
            } else {
                showNotification(result.error || 'Failed to delete user', 'error');
            }
        } catch (error) {
            console.error('Error deleting user:', error);
            showNotification('Error deleting user', 'error');
        }

        // Close dropdown
        const dropdown = document.getElementById(`dropdown-${userId}`);
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }
}

async function searchUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();

    if (searchTerm.trim() === '') {
        filteredUsers = [...users];
    } else {
        try {
            const response = await fetch(`${API_BASE_URL}users_api.php?action=search_users&search=${encodeURIComponent(searchTerm)}`);
            const result = await response.json();

            if (result.success) {
                filteredUsers = result.data.map(user => {
                    // Ensure experience data is properly formatted
                    const expLevel = parseInt(user.expLevel) || 1;
                    const exp = parseInt(user.userExp) || 0;
                    
                    return {
                        id: user.user_id,
                        username: user.username,
                        email: user.email || 'N/A',
                        name: user.name || 'N/A',
                        bio: user.bio || 'No bio available',
                        avatar: user.avatar || null,
                        password: '********',
                        achievements: parseInt(user.achievements) || 0,
                        totalAchievements: parseInt(user.totalAchievements) || 50,
                        levelsCompleted: parseInt(user.levels_completed) || 0,
                        totalLevels: parseInt(user.total_levels) || 6,
                        exp: exp,
                        expLevel: expLevel,
                        isBanned: user.is_banned == 1,
                        bannedFrom: user.banned_from,
                        bannedUntil: user.banned_until,
                        // Calculate experience thresholds based on database values
                        ...calculateExpThresholds(exp, expLevel),
                        joinDate: new Date(user.created_at)
                    };
                });
            } else {
                filteredUsers = users.filter(user =>
                    user.username.toLowerCase().includes(searchTerm) ||
                    user.email.toLowerCase().includes(searchTerm) ||
                    user.name.toLowerCase().includes(searchTerm)
                );
            }
        } catch (error) {
            console.error('Error searching users:', error);
            // Fallback to local search
            filteredUsers = users.filter(user =>
                user.username.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm) ||
                user.name.toLowerCase().includes(searchTerm)
            );
        }
    }

    currentPage = 1;
    displayUsers();
    
    // Re-add hover effects after search results are displayed
    addExpHoverEffects();
}

function changePage(direction) {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        displayUsers();
        // Re-add hover effects after pagination
        addExpHoverEffects();
    }
}

function updatePagination() {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    document.getElementById('pageInfo').textContent = `Page ${currentPage} of ${totalPages}`;
    document.getElementById('prevBtn').disabled = currentPage === 1;
    document.getElementById('nextBtn').disabled = currentPage === totalPages;
}

// Modal functions
function closeModal(modalId) {
    document.getElementById(modalId).classList.remove('show');
}

// Utility Functions
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">&times;</button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Event listeners
document.addEventListener('DOMContentLoaded', async function() {
    await initializeData();

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        if (!event.target.closest('.user-actions')) {
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    // Close modals when clicking outside
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.classList.remove('show');
            }
        });
    });
});

// Add hover effects for experience stats
function addExpHoverEffects() {
    document.querySelectorAll('.exp-stat').forEach(stat => {
        stat.addEventListener('mouseenter', function() {
            const tooltip = this.querySelector('.exp-tooltip');
            if (tooltip) {
                tooltip.style.opacity = '1';
            }
        });
        
        stat.addEventListener('mouseleave', function() {
            const tooltip = this.querySelector('.exp-tooltip');
            if (tooltip) {
                tooltip.style.opacity = '0';
            }
        });
    });
}

// Calculate next level threshold based on experience
function calculateExpThresholds(currentExp, currentLevel) {
    // If no experience levels are loaded yet, use default calculation
    if (expLevels.length === 0) {
        return {
            nextLevelExp: currentLevel * 100,
            currentLevelExp: (currentLevel - 1) * 100,
            isMaxLevel: currentLevel >= maxExpLevel
        };
    }
    
    // Find the current level's experience threshold
    const currentLevelData = expLevels.find(level => parseInt(level.expName) === currentLevel);
    const currentLevelExp = currentLevelData ? parseInt(currentLevelData.expNeeded) : (currentLevel - 1) * 100;
    
    // Find the next level's experience threshold
    const nextLevel = currentLevel + 1;
    const nextLevelData = expLevels.find(level => parseInt(level.expName) === nextLevel);
    const nextLevelExp = nextLevelData ? parseInt(nextLevelData.expNeeded) : currentLevel * 100;
    
    return {
        nextLevelExp: nextLevelExp,
        currentLevelExp: currentLevelExp,
        isMaxLevel: currentLevel >= maxExpLevel
    };
}

// Replace banUser with modal logic
function banUser(userId) {
    banUserId = userId;
    const modal = document.getElementById('banUserModal');
    const modalBody = document.getElementById('banUserModalBody');
    modalBody.innerHTML = `
        <form id="banUserForm" onsubmit="return false;">
            <label for="banUntilInput">Ban until date (required):</label>
            <input type="date" id="banUntilInput" name="banUntilInput" style="height: 40px;" required>
            <div id="banErrorMsg" style="color:red;display:none;margin-top:5px;"></div>
            <div style="margin-top:10px;">
                <button type="button" onclick="confirmBanUser()">Ban User</button>
                <button type="button" onclick="closeModal('banUserModal')">Cancel</button>
            </div>
        </form>
    `;
    modal.classList.add('show');
}

async function confirmBanUser() {
    const banUntilInput = document.getElementById('banUntilInput').value;
    const errorMsg = document.getElementById('banErrorMsg');
    if (!banUntilInput) {
        errorMsg.textContent = 'Ban duration is required.';
        errorMsg.style.display = 'block';
        return;
    } else {
        errorMsg.style.display = 'none';
    }
    let bannedUntil = `${banUntilInput} 23:59:59`;
    if (!banUserId) return;
    try {
        const url = `${API_BASE_URL}users_api.php?action=ban_user&user_id=${banUserId}&banned_until=${encodeURIComponent(bannedUntil)}`;
        const response = await fetch(url, { method: 'POST' });
        const result = await response.json();
        if (result.success && result.data) {
            // Use mapBackendUserToFrontend to ensure consistency
            const updatedUser = mapBackendUserToFrontend(result.data);
            
            users = users.map(user => user.id === banUserId ? updatedUser : user);
            filteredUsers = filteredUsers.map(user => user.id === banUserId ? updatedUser : user);
            
            displayUsers();
            closeModal('userModal'); // Close details modal if open
            showNotification('User banned successfully', 'success');
        } else {
            showNotification(result.error || 'Failed to ban user', 'error');
        }
    } catch (error) {
        console.error('Error banning user:', error);
        showNotification('Error banning user', 'error');
    }
    closeModal('banUserModal');
    // It's important to nullify banUserId after the operation
    const tempBanId = banUserId;
    banUserId = null;
    // Close dropdown using the temporary variable
    const dropdown = document.getElementById(`dropdown-${tempBanId}`);
    if (dropdown) {
        dropdown.classList.remove('show');
    }
}

async function unbanUser(userId) {
    if (confirm('Are you sure you want to unban this user?')) {
        try {
            const response = await fetch(`${API_BASE_URL}users_api.php?action=unban_user&user_id=${userId}`, {
                method: 'POST'
            });
            const result = await response.json();

            if (result.success && result.data) {
                 // Use mapBackendUserToFrontend to ensure consistency
                const updatedUser = mapBackendUserToFrontend(result.data);

                users = users.map(user => user.id === userId ? updatedUser : user);
                filteredUsers = filteredUsers.map(user => user.id === userId ? updatedUser : user);
               
                displayUsers();
                closeModal('userModal'); // Close details modal if open
                showNotification('User unbanned successfully', 'success');
            } else {
                showNotification(result.error || 'Failed to unban user', 'error');
            }
        } catch (error) {
            console.error('Error unbanning user:', error);
            showNotification('Error unbanning user', 'error');
        }

        // Close dropdown
        const dropdown = document.getElementById(`dropdown-${userId}`);
        if (dropdown) {
            dropdown.classList.remove('show');
        }
    }
}

// Approve User
async function approveUser(userId) {
    if (confirm('Are you sure you want to approve this user account?')) {
        try {
            const response = await fetch(`${API_BASE_URL}users_api.php?action=approve_user`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ user_id: userId })
            });
            const result = await response.json();

            if (result.success) {
                // Update user in local arrays
                const updateUser = (user) => {
                    if (user.id === userId) {
                        user.isApproved = 1;
                    }
                    return user;
                };
                users = users.map(updateUser);
                filteredUsers = filteredUsers.map(updateUser);

                displayUsers();
                closeModal('userModal');
                showNotification('User approved successfully', 'success');
            } else {
                showNotification(result.error || 'Failed to approve user', 'error');
            }
        } catch (error) {
            console.error('Error approving user:', error);
            showNotification('Error approving user', 'error');
        }
    }
}

// Reject User
async function rejectUser(userId) {
    if (confirm('Are you sure you want to reject this user account? This action cannot be undone.')) {
        try {
            const response = await fetch(`${API_BASE_URL}users_api.php?action=reject_user`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ user_id: userId })
            });
            const result = await response.json();

            if (result.success) {
                // Update user in local arrays
                const updateUser = (user) => {
                    if (user.id === userId) {
                        user.isApproved = -1;
                    }
                    return user;
                };
                users = users.map(updateUser);
                filteredUsers = filteredUsers.map(updateUser);

                displayUsers();
                closeModal('userModal');
                showNotification('User account rejected', 'success');
            } else {
                showNotification(result.error || 'Failed to reject user', 'error');
            }
        } catch (error) {
            console.error('Error rejecting user:', error);
            showNotification('Error rejecting user', 'error');
        }
    }
}

// Initialize when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeData();
});
