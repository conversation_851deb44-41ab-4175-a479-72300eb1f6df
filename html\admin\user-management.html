<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>User Management</title>
    <link rel="stylesheet" href="../../css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        html, body {
            max-width: 100%;
            overflow-x: hidden;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> Admin Panel</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active">
                    <a href="#">
                        <i class="fas fa-users"></i> Users Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="game-management.html">
                        <i class="fas fa-gamepad"></i> Game Content
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Users Management Section -->
            <section id="users-section" class="content-section active">
                <div class="section-header">
                    <h1>Users Management</h1>
                    <div class="search-bar">
                        <input type="text" id="userSearch" placeholder="Search users..." onkeyup="searchUsers()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>

                <!-- Bulk Operations -->
                <div class="bulk-operations" id="bulkOperations" style="display: none;">
                    <div class="bulk-controls">
                        <span id="selectedCount">0 users selected</span>
                        <button id="bulkApproveBtn" class="bulk-btn approve-btn" onclick="showBulkApprovalModal()">
                            <i class="fas fa-check"></i> Bulk Approve
                        </button>
                        <button id="bulkDisapproveBtn" class="bulk-btn reject-btn" onclick="bulkDisapprove()">
                            <i class="fas fa-times"></i> Bulk Disapprove
                        </button>
                        <button id="clearSelectionBtn" class="bulk-btn clear-btn" onclick="clearSelection()">
                            <i class="fas fa-times-circle"></i> Clear Selection
                        </button>
                    </div>
                </div>

                <!-- Users Grid -->
                <div class="users-grid" id="usersGrid">
                    <!-- Users will be populated by JavaScript -->
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <button id="prevBtn" onclick="changePage(-1)"><i class="fas fa-chevron-left"></i></button>
                    <span id="pageInfo">Page 1 of 1</span>
                    <button id="nextBtn" onclick="changePage(1)"><i class="fas fa-chevron-right"></i></button>
                </div>
            </section>
        </main>
    </div>

    <!-- User Details Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>User Details</h2>
                <span class="close" onclick="closeModal('userModal')">&times;</span>
            </div>
            <div class="modal-actions">
                <button id="editUserBtn" class="action-btn edit-btn"><i class="fas fa-edit"></i> Edit User</button>
                <button id="approveUserBtn" class="action-btn approve-btn" style="display: none;"><i class="fas fa-check"></i> Approve Account</button>
                <button id="rejectUserBtn" class="action-btn reject-btn" style="display: none;"><i class="fas fa-times"></i> Reject Account</button>
                <button id="activateUserBtn" class="action-btn activate-btn" style="display: none;"><i class="fas fa-check-circle"></i> Activate Account</button>
                <button id="deactivateUserBtn" class="action-btn deactivate-btn" style="display: none;"><i class="fas fa-ban"></i> Deactivate Account</button>
                <button id="deleteUserBtn" class="action-btn delete-btn"><i class="fas fa-trash"></i> Delete User</button>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- User details will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Bulk Approval Modal -->
    <div id="bulkApprovalModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Bulk Approve Users</h2>
                <span class="close" onclick="closeModal('bulkApprovalModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="bulkApprovalForm">
                    <div class="form-group">
                        <label for="bulkSemester">Semester:</label>
                        <select id="bulkSemester" name="semester" required>
                            <option value="">Select Semester</option>
                            <option value="1">1st Semester</option>
                            <option value="2">2nd Semester</option>
                            <option value="3">3rd Semester</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bulkSchoolyr">School Year:</label>
                        <input type="date" id="bulkSchoolyr" name="schoolyr" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="confirmBulkApproval()" class="approve-btn">Approve Selected Users</button>
                        <button type="button" onclick="closeModal('bulkApprovalModal')" class="cancel-btn">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div id="editUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Edit User</h2>
                <span class="close" onclick="closeModal('editUserModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="editUserId" name="user_id">
                    <div class="form-group">
                        <label for="editUsername">Username:</label>
                        <input type="text" id="editUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmail">Email:</label>
                        <input type="email" id="editEmail" name="email">
                    </div>
                    <div class="form-group">
                        <label for="editName">Name:</label>
                        <input type="text" id="editName" name="name">
                    </div>
                    <div class="form-group">
                        <label for="editBio">Bio:</label>
                        <textarea id="editBio" name="bio" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editSemester">Semester:</label>
                        <select id="editSemester" name="semester">
                            <option value="">Select Semester</option>
                            <option value="1">1st Semester</option>
                            <option value="2">2nd Semester</option>
                            <option value="3">3rd Semester</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editSchoolyr">School Year:</label>
                        <input type="date" id="editSchoolyr" name="schoolyr">
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="saveUserEdit()" class="save-btn">Save Changes</button>
                        <button type="button" onclick="closeModal('editUserModal')" class="cancel-btn">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Approval Modal -->
    <div id="approvalModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Approve User</h2>
                <span class="close" onclick="closeModal('approvalModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="approvalForm">
                    <input type="hidden" id="approvalUserId" name="user_id">
                    <div class="form-group">
                        <label for="approvalSemester">Semester:</label>
                        <select id="approvalSemester" name="semester" required>
                            <option value="">Select Semester</option>
                            <option value="1">1st Semester</option>
                            <option value="2">2nd Semester</option>
                            <option value="3">3rd Semester</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="approvalSchoolyr">School Year:</label>
                        <input type="date" id="approvalSchoolyr" name="schoolyr" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="confirmApproval()" class="approve-btn">Approve User</button>
                        <button type="button" onclick="closeModal('approvalModal')" class="cancel-btn">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="../../js/admin/user-management.js"></script>
</body>
</html>
