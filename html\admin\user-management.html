<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>User Management</title>
    <link rel="stylesheet" href="../../css/admin/admin-dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        html, body {
            max-width: 100%;
            overflow-x: hidden;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> Admin Panel</h2>
            </div>
            <ul class="nav-menu">
                <li class="nav-item active">
                    <a href="#">
                        <i class="fas fa-users"></i> Users Management
                    </a>
                </li>
                <li class="nav-item">
                    <a href="game-management.html">
                        <i class="fas fa-gamepad"></i> Game Content
                    </a>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Users Management Section -->
            <section id="users-section" class="content-section active">
                <div class="section-header">
                    <h1>Users Management</h1>
                    <div class="search-bar">
                        <input type="text" id="userSearch" placeholder="Search users..." onkeyup="searchUsers()">
                        <i class="fas fa-search"></i>
                    </div>
                </div>

                <!-- Users Grid -->
                <div class="users-grid" id="usersGrid">
                    <!-- Users will be populated by JavaScript -->
                </div>

                <!-- Pagination -->
                <div class="pagination">
                    <button id="prevBtn" onclick="changePage(-1)"><i class="fas fa-chevron-left"></i></button>
                    <span id="pageInfo">Page 1 of 1</span>
                    <button id="nextBtn" onclick="changePage(1)"><i class="fas fa-chevron-right"></i></button>
                </div>
            </section>
        </main>
    </div>

    <!-- User Details Modal -->
    <div id="userModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>User Details</h2>
                <span class="close" onclick="closeModal('userModal')">&times;</span>
            </div>
            <div class="modal-actions">
                <button id="approveUserBtn" class="action-btn approve-btn" style="display: none;"><i class="fas fa-check"></i> Approve Account</button>
                <button id="rejectUserBtn" class="action-btn reject-btn" style="display: none;"><i class="fas fa-times"></i> Reject Account</button>
                <button id="banUserBtn" class="action-btn ban-btn"><i class="fas fa-ban"></i> Ban User</button>
                <button id="deleteUserBtn" class="action-btn delete-btn"><i class="fas fa-trash"></i> Delete User</button>
            </div>
            <div class="modal-body" id="userModalBody">
                <!-- User details will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Ban User Modal -->
    <div id="banUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Ban User</h2>
                <span class="close" onclick="closeModal('banUserModal')">&times;</span>
            </div>
            <div class="modal-body" id="banUserModalBody">
                <!-- Ban form will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <script src="../../js/admin/user-management.js"></script>
</body>
</html>
