<?php
session_start();
require_once 'dbconnection.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $password = $_POST['password'];
    $username = $_POST['username'];

    try {
        // Initialize database connection
        $database = new Database();
        $conn = $database->getConnection();

        // Prepare and execute
        $stmt = $conn->prepare("SELECT user_id, username, password, is_banned, banned_from, banned_until, is_approved FROM user_account WHERE username = ?");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        $passwordMatch = false;
        if ($user) {
            // Verify password
            if (password_verify($password, $user['password'])) {
                $passwordMatch = true;
            }
            // Fallback for plain-text passwords
            else if ($password === $user['password']) {
                $passwordMatch = true;
                // Upgrade plain-text password to hashed
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $updateStmt = $conn->prepare("UPDATE user_account SET password = ? WHERE user_id = ?");
                $updateStmt->execute([$hashedPassword, $user['user_id']]);
            }
        }
        
        if ($passwordMatch) {
            // Approval check
            if (!isset($user['is_approved']) || $user['is_approved'] == 0) {
                header("Location: ../html/login.html?error=pending_approval");
                exit;
            }

            // Rejection check
            if ($user['is_approved'] == -1) {
                header("Location: ../html/login.html?error=account_rejected");
                exit;
            }

            // Ban check
            if (isset($user['is_banned']) && $user['is_banned']) {
                $now = date('Y-m-d H:i:s');
                $bannedUntil = $user['banned_until'];
                // If banned_until is null (permanent) or in the future, deny login
                if (is_null($bannedUntil) || $bannedUntil === '' || $bannedUntil > $now) {
                    header("Location: ../html/login.html?error=banned");
                    exit;
                }
            }
            // After successful login
            $_SESSION['userId'] = $user['user_id'];
            header("Location: ../html/mainpage.html");
            exit;
        } else {
            // Redirect with error parameter
            header("Location: ../html/login.html?error=invalid_credentials");
            exit;
        }
    } catch(PDOException $e) {
        // Redirect with error parameter
        header("Location: ../html/login.html?error=db_error");
        exit;
    }
}
?>

<script>
    let login_fail = `<?php echo $message; ?>`;
    console.log(login_fail);
    if (login_fail === "1") {
        triggerButton();
    }

    function triggerButton() {
        document.getElementById("warning_modal").click();
    };

    function returnButton() {
        window.location.href = '../html/login.html'
    }
</script>