<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Management System Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>User Management System Test</h1>
    
    <div class="test-section">
        <h2>Database Connection Test</h2>
        <button onclick="testDatabaseConnection()">Test Database Connection</button>
        <div id="db-test-result"></div>
    </div>

    <div class="test-section">
        <h2>User API Tests</h2>
        <button onclick="testGetAllUsers()">Test Get All Users</button>
        <button onclick="testUserApproval()">Test User Approval</button>
        <button onclick="testUserEdit()">Test User Edit</button>
        <button onclick="testActivateDeactivate()">Test Activate/Deactivate</button>
        <div id="api-test-result"></div>
    </div>

    <div class="test-section">
        <h2>Login System Test</h2>
        <div>
            <input type="text" id="test-username" placeholder="Username" class="test-input">
            <input type="password" id="test-password" placeholder="Password" class="test-input">
            <button onclick="testLogin()">Test Login</button>
        </div>
        <div id="login-test-result"></div>
    </div>

    <div class="test-section">
        <h2>Bulk Operations Test</h2>
        <div>
            <input type="text" id="bulk-user-ids" placeholder="User IDs (comma separated)" class="test-input">
            <select id="bulk-semester" class="test-input">
                <option value="">Select Semester</option>
                <option value="1">1st Semester</option>
                <option value="2">2nd Semester</option>
                <option value="3">3rd Semester</option>
            </select>
            <input type="date" id="bulk-schoolyr" class="test-input">
            <button onclick="testBulkApproval()">Test Bulk Approval</button>
            <button onclick="testBulkDisapproval()">Test Bulk Disapproval</button>
        </div>
        <div id="bulk-test-result"></div>
    </div>

    <script>
        const API_BASE_URL = 'php/admin/';

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        async function testDatabaseConnection() {
            try {
                const response = await fetch(`${API_BASE_URL}users_api.php?action=get_all_users`);
                const result = await response.json();
                
                if (result.success) {
                    showResult('db-test-result', `✅ Database connection successful! Found ${result.data.length} users.`, 'success');
                } else {
                    showResult('db-test-result', `❌ Database connection failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('db-test-result', `❌ Connection error: ${error.message}`, 'error');
            }
        }

        async function testGetAllUsers() {
            try {
                const response = await fetch(`${API_BASE_URL}users_api.php?action=get_all_users`);
                const result = await response.json();
                
                if (result.success) {
                    const users = result.data;
                    let message = `✅ Retrieved ${users.length} users successfully!\n\n`;
                    
                    // Show first few users as sample
                    users.slice(0, 3).forEach(user => {
                        message += `User: ${user.username} | Email: ${user.email || 'N/A'} | Approved: ${user.is_approved} | Semester: ${user.semester || 'N/A'} | School Year: ${user.schoolyr || 'N/A'}\n`;
                    });
                    
                    showResult('api-test-result', message.replace(/\n/g, '<br>'), 'success');
                } else {
                    showResult('api-test-result', `❌ Failed to get users: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('api-test-result', `❌ API error: ${error.message}`, 'error');
            }
        }

        async function testUserApproval() {
            // This is a mock test - in real scenario you'd need a pending user ID
            showResult('api-test-result', '⚠️ User approval test requires a pending user ID. Check the admin panel for pending users.', 'info');
        }

        async function testUserEdit() {
            // This is a mock test - in real scenario you'd need a user ID
            showResult('api-test-result', '⚠️ User edit test requires a user ID. Check the admin panel for existing users.', 'info');
        }

        async function testActivateDeactivate() {
            // This is a mock test - in real scenario you'd need a user ID
            showResult('api-test-result', '⚠️ Activate/Deactivate test requires a user ID. Check the admin panel for existing users.', 'info');
        }

        async function testLogin() {
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            
            if (!username || !password) {
                showResult('login-test-result', '❌ Please enter both username and password', 'error');
                return;
            }
            
            try {
                const formData = new FormData();
                formData.append('username', username);
                formData.append('password', password);
                
                const response = await fetch('php/login.php', {
                    method: 'POST',
                    body: formData
                });
                
                // Check if redirected (successful login) or returned with error
                if (response.redirected) {
                    showResult('login-test-result', '✅ Login successful! (Would redirect to main page)', 'success');
                } else {
                    const url = new URL(response.url);
                    const error = url.searchParams.get('error');
                    if (error) {
                        showResult('login-test-result', `❌ Login failed: ${error}`, 'error');
                    } else {
                        showResult('login-test-result', '⚠️ Unexpected response from login', 'info');
                    }
                }
            } catch (error) {
                showResult('login-test-result', `❌ Login test error: ${error.message}`, 'error');
            }
        }

        async function testBulkApproval() {
            const userIds = document.getElementById('bulk-user-ids').value.split(',').map(id => id.trim()).filter(id => id);
            const semester = document.getElementById('bulk-semester').value;
            const schoolyr = document.getElementById('bulk-schoolyr').value;
            
            if (userIds.length === 0 || !semester || !schoolyr) {
                showResult('bulk-test-result', '❌ Please fill in all fields for bulk approval test', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}users_api.php?action=bulk_approve`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_ids: userIds,
                        semester: semester,
                        schoolyr: schoolyr
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('bulk-test-result', `✅ Bulk approval successful! Affected ${result.affected_rows} users.`, 'success');
                } else {
                    showResult('bulk-test-result', `❌ Bulk approval failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('bulk-test-result', `❌ Bulk approval error: ${error.message}`, 'error');
            }
        }

        async function testBulkDisapproval() {
            const userIds = document.getElementById('bulk-user-ids').value.split(',').map(id => id.trim()).filter(id => id);
            
            if (userIds.length === 0) {
                showResult('bulk-test-result', '❌ Please enter user IDs for bulk disapproval test', 'error');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE_URL}users_api.php?action=bulk_disapprove`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_ids: userIds
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('bulk-test-result', `✅ Bulk disapproval successful! Affected ${result.affected_rows} users.`, 'success');
                } else {
                    showResult('bulk-test-result', `❌ Bulk disapproval failed: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('bulk-test-result', `❌ Bulk disapproval error: ${error.message}`, 'error');
            }
        }

        // Auto-run database connection test on page load
        window.addEventListener('load', testDatabaseConnection);
    </script>
</body>
</html>
